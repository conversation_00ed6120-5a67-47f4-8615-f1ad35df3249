<?xml version="1.0" encoding="utf-8"?>
<!--
 Copyright 2023 Google LLC

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

https://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
-->

<resources>
    <color name="jetstream_background">#1A1C1E</color>
    <color name="primary">#A8C8FF</color>
    <color name="onPrimary">#003062</color>
    <color name="primaryContainer">#00468A</color>
    <color name="onPrimaryContainer">#D6E3FF</color>
    <color name="secondary">#BDC7DC</color>
    <color name="onSecondary">#273141</color>
    <color name="secondaryContainer">#3E4758</color>
    <color name="onSecondaryContainer">#D9E3F8</color>
    <color name="tertiary">#DCBCE1</color>
    <color name="onTertiary">#3E2845</color>
    <color name="tertiaryContainer">#563E5C</color>
    <color name="onTertiaryContainer">#F9D8FE</color>
    <color name="background">#1A1C1E</color>
    <color name="onBackground">#E3E2E6</color>
    <color name="surface">#1A1C1E</color>
    <color name="onSurface">#E3E2E6</color>
    <color name="surfaceVariant">#43474E</color>
    <color name="onSurfaceVariant">#C4C6CF</color>
    <color name="error">#FFB4AB</color>
    <color name="onError">#690005</color>
    <color name="errorContainer">#93000A</color>
    <color name="onErrorContainer">#FFB4AB</color>
    <color name="border">#8E9099</color>
    <color name="ic_launcher_background">#1A1C1E</color>
</resources>
