/*
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.jetstream.presentation.utils

import androidx.compose.ui.graphics.Color

val Coral = Color(0xFFF3A397)
val LightYellow = Color(0xFFF8EE94)

val Red300 = Color(0xFFE57373)
val Pink300 = Color(0xFFF06292)
val Purple300 = Color(0xFFBA68C8)
val DeepPurple300 = Color(0xFF9575CD)
val Indigo300 = Color(0xFF7986CB)
val Blue300 = Color(0xFF64B5F6)
val LightBlue300 = Color(0xFF4FC3F7)
val Cyan300 = Color(0xFF4DD0E1)
val Teal300 = Color(0xFF4DB6AC)
val Green300 = Color(0xFF81C784)
val LightGreen300 = Color(0xFFAED581)
val Lime300 = Color(0xFFDCE775)
val Yellow300 = Color(0xFFFFF176)
val Amber300 = Color(0xFFFFD54F)
val Orange300 = Color(0xFFFFB74D)
val DeepOrange300 = Color(0xFFFF8A65)
val Brown300 = Color(0xFFA1887F)
val Gray300 = Color(0xFFE0E0E0)
val BlueGray300 = Color(0xFF90A4AE)

val ourColors = listOf(
    Coral,
    LightYellow,
    Red300,
    Pink300,
    Purple300,
    DeepPurple300,
    Indigo300,
    Blue300,
    LightBlue300,
    Cyan300,
    Teal300,
    Green300,
    LightGreen300,
    Lime300,
    Yellow300,
    Amber300,
    Orange300,
    DeepOrange300,
    Brown300,
    Gray300,
    BlueGray300,
)
