[versions]
activity-compose = "1.10.1"
android-gradle-plugin = "8.8.2"
android-test-plugin = "8.8.2"
androidx-baselineprofile = "1.3.3"
benchmark-macro-junit4 = "1.3.3"
coil-compose = "2.7.0"
compose-bom = "2025.02.00"
tv-material = "1.0.0"
core-ktx = "1.15.0"
core-splashscreen = "1.0.1"
hilt-navigation-compose = "1.2.0"
hilt-android = "2.54"
junit = "1.2.1"
kotlin-android = "2.1.0"
kotlinx-serialization = "1.8.0"
ksp = "2.1.0-1.0.29"
lifecycle-runtime-ktx = "2.8.7"
media3 = "1.6.0-beta01"
navigation-compose = "2.8.8"
profileinstaller = "1.4.1"
uiautomator = "2.3.0"
rules = "1.6.1"
sardine-android = "0.9"
room = "2.6.1"

[libraries]
androidx-activity-compose = { module = "androidx.activity:activity-compose", version.ref = "activity-compose" }
androidx-benchmark-macro-junit4 = { module = "androidx.benchmark:benchmark-macro-junit4", version.ref = "benchmark-macro-junit4" }
androidx-compose-bom = { module = "androidx.compose:compose-bom", version.ref = "compose-bom" }
androidx-compose-ui-tooling = { module = "androidx.compose.ui:ui-tooling" }
androidx-compose-ui-tooling-preview = { module = "androidx.compose.ui:ui-tooling-preview" }
androidx-compose-runtime = { module = "androidx.compose.runtime:runtime" }
androidx-core-ktx = { module = "androidx.core:core-ktx", version.ref = "core-ktx" }
androidx-core-splashscreen = { module = "androidx.core:core-splashscreen", version.ref = "core-splashscreen" }
androidx-hilt-navigation-compose = { module = "androidx.hilt:hilt-navigation-compose", version.ref = "hilt-navigation-compose" }
androidx-junit = { module = "androidx.test.ext:junit", version.ref = "junit" }
androidx-lifecycle-runtime-compose = { module = "androidx.lifecycle:lifecycle-runtime-compose", version.ref = "lifecycle-runtime-ktx" }
androidx-lifecycle-runtime-ktx = { module = "androidx.lifecycle:lifecycle-runtime-ktx", version.ref = "lifecycle-runtime-ktx" }
androidx-lifecycle-viewmodel-compose = { module = "androidx.lifecycle:lifecycle-viewmodel-compose", version.ref = "lifecycle-runtime-ktx" }
androidx-material-icons-extended = { module = "androidx.compose.material:material-icons-extended" }
androidx-media3-ui = { module = "androidx.media3:media3-ui-compose", version.ref = "media3" }
androidx-media3-exoplayer = { module = "androidx.media3:media3-exoplayer", version.ref = "media3" }
androidx-navigation-compose = { module = "androidx.navigation:navigation-compose", version.ref = "navigation-compose" }
androidx-profileinstaller = { module = "androidx.profileinstaller:profileinstaller", version.ref = "profileinstaller" }
androidx-tv-material = { module = "androidx.tv:tv-material", version.ref = "tv-material" }
androidx-uiautomator = { module = "androidx.test.uiautomator:uiautomator", version.ref = "uiautomator" }
coil-compose = { module = "io.coil-kt:coil-compose", version.ref = "coil-compose" }
hilt-android = { module = "com.google.dagger:hilt-android", version.ref = "hilt-android" }
hilt-compiler = { module = "com.google.dagger:hilt-compiler", version.ref = "hilt-android" }
kotlinx-serialization = { module = "org.jetbrains.kotlinx:kotlinx-serialization-json", version.ref = "kotlinx-serialization" }
androidx-rules = { group = "androidx.test", name = "rules", version.ref = "rules" }
sardine-android = { module = "com.github.thegrizzlylabs:sardine-android", version.ref = "sardine-android" }
androidx-room-runtime = { module = "androidx.room:room-runtime", version.ref = "room" }
androidx-room-compiler = { module = "androidx.room:room-compiler", version.ref = "room" }
androidx-room-ktx = { module = "androidx.room:room-ktx", version.ref = "room" }

[plugins]
android-application = { id = "com.android.application", version.ref = "android-gradle-plugin" }
android-test = { id = "com.android.test", version.ref = "android-test-plugin" }
androidx-baselineprofile = { id = "androidx.baselineprofile", version.ref = "androidx-baselineprofile" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin-android" }
kotlin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin-android" }
compose-compiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin-android" }
hilt = { id = "com.google.dagger.hilt.android", version.ref = "hilt-android" }
ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }
